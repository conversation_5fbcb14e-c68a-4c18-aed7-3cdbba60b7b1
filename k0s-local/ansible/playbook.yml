---
- name: Deploy K0s Single-Node Kubernetes Cluster
  hosts: k0s_cluster
  become: yes
  gather_facts: yes
  vars:
    k0s_version: "v1.28.4+k0s.0"
    k0s_config_dir: "/etc/k0s"
    k0s_data_dir: "/var/lib/k0s"
    cluster_name: "k0s-local"

  tasks:
    - name: Wait for system to be ready
      wait_for_connection:
        timeout: 300

    - name: Gather system facts
      setup:

    - name: Check if k0s is already installed
      stat:
        path: /usr/local/bin/k0s
      register: k0s_binary

    - name: Download and install k0s binary
      shell: |
        curl -sSLf https://get.k0s.sh | sudo sh
      when: not k0s_binary.stat.exists

    - name: Ensure k0s config directory exists
      file:
        path: "/etc/k0s"
        state: directory
        owner: root
        group: root
        mode: '0755'

    - name: Create k0s configuration file
      template:
        src: k0s-config.yaml.j2
        dest: "/etc/k0s/k0s.yaml"
        owner: root
        group: root
        mode: '0644'
      notify: restart k0s

    - name: Check if k0s controller is already running
      shell: k0s status
      register: k0s_status_check
      failed_when: false
      changed_when: false

    - name: Kill any stale k0s processes
      shell: pkill -f "k0s controller" || true
      when: k0s_status_check.rc != 0
      failed_when: false

    - name: Start k0s controller in background
      shell: nohup k0s controller --config /etc/k0s/k0s.yaml > /var/log/k0s-controller.log 2>&1 &
      when: k0s_status_check.rc != 0
      async: 10
      poll: 0

    - name: Wait for k0s to start
      pause:
        seconds: 15

    - name: Wait for k0s API server to be ready
      shell: k0s kubectl get nodes
      register: api_result
      until: api_result.rc == 0
      retries: 30
      delay: 10
      failed_when: false

    - name: Generate kubeconfig for admin access
      command: k0s kubeconfig admin
      register: kubeconfig_output
      changed_when: false

    - name: Save kubeconfig to file
      copy:
        content: "{{ kubeconfig_output.stdout }}"
        dest: "/home/<USER>/kubeconfig"
        owner: "k0s"
        group: "k0s"
        mode: '0600'

    - name: Create kubectl alias for k0s user
      lineinfile:
        path: "/home/<USER>/.bashrc"
        line: "alias kubectl='k0s kubectl --kubeconfig=/home/<USER>/kubeconfig'"
        create: yes
        owner: "k0s"
        group: "k0s"

    - name: Set KUBECONFIG environment variable
      lineinfile:
        path: "/home/<USER>/.bashrc"
        line: "export KUBECONFIG=/home/<USER>/kubeconfig"
        create: yes
        owner: "k0s"
        group: "k0s"

    - name: Display cluster status
      command: k0s status
      register: cluster_status
      changed_when: false

    - name: Show cluster information
      debug:
        msg: |
          K0s cluster is ready!
          Cluster status: {{ cluster_status.stdout }}
          
          To access the cluster:
          1. SSH into the container: ssh -p 2222 k0s@localhost
          2. Use kubectl: k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get nodes
          
          Or copy the kubeconfig to your local machine:
          scp -P 2222 k0s@localhost:/home/<USER>/kubeconfig ~/.kube/k0s-local-config

  handlers:
    - name: restart k0s
      shell: |
        pkill -f "k0s controller" || true
        sleep 5
        nohup k0s controller --config /etc/k0s/k0s.yaml > /var/log/k0s-controller.log 2>&1 &
