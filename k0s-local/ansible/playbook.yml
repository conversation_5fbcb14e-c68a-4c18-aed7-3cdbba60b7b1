---
- name: Deploy K0s Single-Node Kubernetes Cluster
  hosts: k0s_cluster
  become: yes
  gather_facts: yes
  vars:
    k0s_version: "{{ k0s_version | default('v1.28.4+k0s.0') }}"
    k0s_config_dir: "{{ k0s_config_dir | default('/etc/k0s') }}"
    k0s_data_dir: "{{ k0s_data_dir | default('/var/lib/k0s') }}"
    cluster_name: "{{ cluster_name | default('k0s-local') }}"

  tasks:
    - name: Wait for system to be ready
      wait_for_connection:
        timeout: 300

    - name: Gather system facts
      setup:

    - name: Check if k0s is already installed
      stat:
        path: /usr/local/bin/k0s
      register: k0s_binary

    - name: Download and install k0s binary
      shell: |
        curl -sSLf https://get.k0s.sh | sudo sh
      when: not k0s_binary.stat.exists

    - name: Ensure k0s config directory exists
      file:
        path: "{{ k0s_config_dir }}"
        state: directory
        owner: root
        group: root
        mode: '0755'

    - name: Create k0s configuration file
      template:
        src: k0s-config.yaml.j2
        dest: "{{ k0s_config_dir }}/k0s.yaml"
        owner: root
        group: root
        mode: '0644'
      notify: restart k0s

    - name: Install k0s as controller
      command: k0s install controller --config {{ k0s_config_dir }}/k0s.yaml
      args:
        creates: /etc/systemd/system/k0scontroller.service

    - name: Start and enable k0s controller service
      systemd:
        name: k0scontroller
        state: started
        enabled: yes
        daemon_reload: yes

    - name: Wait for k0s API server to be ready
      uri:
        url: "http://localhost:8080/readyz"
        method: GET
      register: api_result
      until: api_result.status == 200
      retries: 30
      delay: 10
      ignore_errors: yes

    - name: Generate kubeconfig for admin access
      command: k0s kubeconfig admin
      register: kubeconfig_output
      changed_when: false

    - name: Save kubeconfig to file
      copy:
        content: "{{ kubeconfig_output.stdout }}"
        dest: "/home/<USER>/kubeconfig"
        owner: "{{ k0s_user }}"
        group: "{{ k0s_user }}"
        mode: '0600'

    - name: Create kubectl alias for k0s user
      lineinfile:
        path: "/home/<USER>/.bashrc"
        line: "alias kubectl='k0s kubectl --kubeconfig=/home/<USER>/kubeconfig'"
        create: yes
        owner: "{{ k0s_user }}"
        group: "{{ k0s_user }}"

    - name: Set KUBECONFIG environment variable
      lineinfile:
        path: "/home/<USER>/.bashrc"
        line: "export KUBECONFIG=/home/<USER>/kubeconfig"
        create: yes
        owner: "{{ k0s_user }}"
        group: "{{ k0s_user }}"

    - name: Display cluster status
      command: k0s status
      register: cluster_status
      changed_when: false

    - name: Show cluster information
      debug:
        msg: |
          K0s cluster is ready!
          Cluster status: {{ cluster_status.stdout }}
          
          To access the cluster:
          1. SSH into the container: ssh -p 2222 k0s@localhost
          2. Use kubectl: k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get nodes
          
          Or copy the kubeconfig to your local machine:
          scp -P 2222 k0s@localhost:/home/<USER>/kubeconfig ~/.kube/k0s-local-config

  handlers:
    - name: restart k0s
      systemd:
        name: k0scontroller
        state: restarted
