#!/bin/bash

# This script sets up the systemd service for k0s initialization
# It runs during container build to create the service file

# Create k0s config directory
mkdir -p /etc/k0s

# Generate k0s configuration
cat > /etc/k0s/k0s.yaml << EOF
apiVersion: k0s.k0sproject.io/v1beta1
kind: ClusterConfig
metadata:
  name: k0s-local
spec:
  api:
    address: 0.0.0.0
    port: 6443
    sans:
      - 127.0.0.1
      - localhost
      - k0s-local
  controllerManager: {}
  scheduler: {}
  storage:
    type: etcd
  network:
    kubeProxy:
      disabled: false
    kuberouter:
      autoMTU: true
    podCIDR: **********/16
    serviceCIDR: *********/12
  podSecurityPolicy:
    defaultPolicy: 00-k0s-privileged
  workerProfiles:
  - name: default
    values:
      kubelet:
        cgroupsPerQOS: true
        cgroupDriver: systemd
EOF

# Create a systemd service to initialize SSH and other services
cat > /etc/systemd/system/k0s-init.service << 'EOF'
[Unit]
Description=K0s Initialization Service
After=multi-user.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'systemctl enable ssh && systemctl start ssh'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# Note: The service will be enabled when systemd starts in the container
