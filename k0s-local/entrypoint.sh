#!/bin/bash

# Start systemd services
systemctl enable ssh
systemctl start ssh

# Create k0s config directory
mkdir -p /etc/k0s

# Generate k0s configuration
cat > /etc/k0s/k0s.yaml << EOF
apiVersion: k0s.k0sproject.io/v1beta1
kind: ClusterConfig
metadata:
  name: k0s-local
spec:
  api:
    address: 0.0.0.0
    port: 6443
    sans:
      - 127.0.0.1
      - localhost
      - k0s-local
  controllerManager: {}
  scheduler: {}
  storage:
    type: etcd
  network:
    kubeProxy:
      disabled: false
    kuberouter:
      autoMTU: true
    podCIDR: **********/16
    serviceCIDR: *********/12
  podSecurityPolicy:
    defaultPolicy: 00-k0s-privileged
  workerProfiles:
  - name: default
    values:
      kubelet:
        cgroupsPerQOS: true
        cgroupDriver: systemd
EOF

# Keep container running
exec /sbin/init
