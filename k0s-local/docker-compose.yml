
services:
  k0s-local:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: k0s-local
    hostname: k0s-local
    privileged: true
    restart: unless-stopped
    ports:
      - "2222:22"      # SSH access
      - "6443:6443"    # Kubernetes API server
      - "8080:8080"    # K0s API (if needed)
      - "10250:10250"  # Kubelet API
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:rw
      - k0s-data:/var/lib/k0s
      - k0s-config:/etc/k0s
      - k0s-kubeconfig:/var/lib/k0s/pki
    environment:
      - K0S_CONFIG_FILE=/etc/k0s/k0s.yaml
    networks:
      - k0s-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/readyz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  k0s-data:
    driver: local
  k0s-config:
    driver: local
  k0s-kubeconfig:
    driver: local

networks:
  k0s-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
