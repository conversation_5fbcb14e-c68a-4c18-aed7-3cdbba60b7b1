
services:
  k0s-local:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: k0s-local
    hostname: k0s-local
    privileged: true
    restart: unless-stopped
    init: true
    tmpfs:
      - /tmp
      - /run
      - /run/lock
    ports:
      - "2222:22"      # SSH access
      - "6443:6443"    # Kubernetes API server
      - "10250:10250"  # Kubelet API
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:rw
      - k0s-data:/var/lib/k0s
      - k0s-config:/etc/k0s
    cap_add:
      - SYS_ADMIN
      - SYS_RESOURCE
    environment:
      - K0S_CONFIG_FILE=/etc/k0s/k0s.yaml
    networks:
      - k0s-network
    healthcheck:
      test: ["CMD", "pgrep", "-f", "sshd"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  k0s-data:
    driver: local
  k0s-config:
    driver: local

networks:
  k0s-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
