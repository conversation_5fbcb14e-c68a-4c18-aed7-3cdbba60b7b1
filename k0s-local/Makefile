# K0s Local Cluster Management

.PHONY: help build start stop restart logs ssh deploy status clean kubeconfig

# Default target
help:
	@echo "K0s Local Cluster Management"
	@echo ""
	@echo "Available targets:"
	@echo "  build      - Build the K0s container image"
	@echo "  start      - Start the K0s cluster container"
	@echo "  stop       - Stop the K0s cluster container"
	@echo "  restart    - Restart the K0s cluster container"
	@echo "  logs       - Show container logs"
	@echo "  ssh        - SSH into the K0s container"
	@echo "  deploy     - Deploy K0s using Ansible"
	@echo "  status     - Show cluster status"
	@echo "  kubeconfig - Copy kubeconfig to local machine"
	@echo "  clean      - Stop and remove all containers and volumes"
	@echo "  help       - Show this help message"

# Build the container image
build:
	docker-compose build

# Start the cluster
start:
	docker-compose up -d

# Stop the cluster
stop:
	docker-compose down

# Restart the cluster
restart: stop start

# Show container logs
logs:
	docker-compose logs -f k0s-local

# SSH into the container
ssh:
	ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost

# Deploy K0s using Ansible
deploy:
	@echo "Waiting for container to be ready..."
	@sleep 10
	ansible-playbook -i ansible/inventory.ini ansible/playbook.yml

# Show cluster status
status:
	@echo "=== Container Status ==="
	docker-compose ps
	@echo ""
	@echo "=== K0s Status (if deployed) ==="
	-ssh -p 2222 -o StrictHostKeyChecking=no -o ConnectTimeout=5 k0s@localhost "k0s status" 2>/dev/null || echo "K0s not yet deployed or container not ready"

# Copy kubeconfig to local machine
kubeconfig:
	@mkdir -p ~/.kube
	scp -P 2222 -o StrictHostKeyChecking=no k0s@localhost:/home/<USER>/kubeconfig ~/.kube/k0s-local-config
	@echo "Kubeconfig copied to ~/.kube/k0s-local-config"
	@echo "Use: kubectl --kubeconfig ~/.kube/k0s-local-config get nodes"

# Clean up everything
clean:
	docker-compose down -v
	docker rmi k0s-local_k0s-local 2>/dev/null || true

# Full setup (build, start, deploy)
setup: build start deploy
	@echo ""
	@echo "=== K0s Cluster Setup Complete! ==="
	@echo ""
	@echo "To access your cluster:"
	@echo "1. SSH: make ssh"
	@echo "2. Get kubeconfig: make kubeconfig"
	@echo "3. Check status: make status"
