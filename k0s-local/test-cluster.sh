#!/bin/bash

# K0s Local Cluster Test Script
set -e

echo "🚀 Testing K0s Local Cluster Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Test 1: Check if Docker is running
echo "1. Checking Docker..."
if docker info >/dev/null 2>&1; then
    print_status "Docker is running"
else
    print_error "Docker is not running. Please start Docker Desktop."
    exit 1
fi

# Test 2: Check if container is running
echo "2. Checking container status..."
if docker-compose ps | grep -q "k0s-local.*Up"; then
    print_status "K0s container is running"
else
    print_warning "K0s container is not running. Starting it..."
    docker-compose up -d
    echo "Waiting for container to initialize..."
    sleep 15
fi

# Test 3: Check SSH connectivity
echo "3. Testing SSH connectivity..."
if ssh -p 2222 -o StrictHostKeyChecking=no -o ConnectTimeout=10 -o BatchMode=yes k0s@localhost "echo 'SSH OK'" 2>/dev/null; then
    print_status "SSH connection successful"
else
    print_error "SSH connection failed. Container may not be ready yet."
    echo "Try running: docker-compose logs k0s-local"
    exit 1
fi

# Test 4: Check if K0s is installed
echo "4. Checking K0s installation..."
if ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "which k0s" >/dev/null 2>&1; then
    print_status "K0s binary is installed"
else
    print_warning "K0s not installed. Running Ansible deployment..."
    ansible-playbook -i ansible/inventory.ini ansible/playbook.yml
fi

# Test 5: Check K0s service status
echo "5. Checking K0s service..."
if ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "sudo systemctl is-active k0scontroller" | grep -q "active"; then
    print_status "K0s controller service is active"
else
    print_warning "K0s controller service is not active"
fi

# Test 6: Test Kubernetes API
echo "6. Testing Kubernetes API..."
if ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get nodes" >/dev/null 2>&1; then
    print_status "Kubernetes API is responding"
    
    # Show cluster info
    echo ""
    echo "📊 Cluster Information:"
    echo "======================"
    ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get nodes -o wide"
    echo ""
    ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get pods --all-namespaces"
else
    print_error "Kubernetes API is not responding"
    echo "Check logs with: docker-compose logs k0s-local"
fi

# Test 7: Test sample deployment
echo ""
echo "7. Testing sample deployment..."
ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "k0s kubectl --kubeconfig=/home/<USER>/kubeconfig run test-pod --image=nginx --restart=Never" 2>/dev/null || true
sleep 5
if ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get pod test-pod" | grep -q "Running\|Pending"; then
    print_status "Sample pod deployment successful"
    ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "k0s kubectl --kubeconfig=/home/<USER>/kubeconfig delete pod test-pod" >/dev/null 2>&1
else
    print_warning "Sample pod deployment failed or still starting"
fi

echo ""
echo "🎉 K0s Local Cluster Test Complete!"
echo "==================================="
echo ""
echo "Next steps:"
echo "• SSH into cluster: ssh -p 2222 k0s@localhost"
echo "• Copy kubeconfig: make kubeconfig"
echo "• View logs: make logs"
echo "• Check status: make status"
echo ""
echo "Happy Kubernetes development! 🚢"
