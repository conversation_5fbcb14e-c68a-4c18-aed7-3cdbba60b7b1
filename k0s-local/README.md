# K0s Local Kubernetes Cluster

This project provides a complete setup for running a K0s single-node Kubernetes cluster inside a Docker container on your Mac, with automated setup using Ansible.

## Architecture

```
k0s-local/
├── Dockerfile              # Container definition for K0s
├── docker-compose.yml      # Docker Compose configuration
├── entrypoint.sh          # Container startup script
├── README.md              # This file
└── ansible/
    ├── inventory.ini      # Ansible inventory
    ├── playbook.yml       # Ansible playbook for K0s setup
    └── templates/
        └── k0s-config.yaml.j2  # K0s configuration template
```

## Prerequisites

- Docker Desktop installed and running
- Docker Compose
- Ansible installed on your Mac
- SSH client

## Quick Start

### 1. Build and Start the Container

```bash
cd k0s-local
docker-compose up -d --build
```

This will:
- Build the K0s container image
- Start the container with systemd
- Expose necessary ports (SSH: 2222, K8s API: 6443)

### 2. Wait for Container to Initialize

```bash
# Check container status
docker-compose ps

# Check container logs
docker-compose logs -f k0s-local
```

### 3. Run Ansible Playbook

```bash
# Install K0s and configure the cluster
ansible-playbook -i ansible/inventory.ini ansible/playbook.yml
```

### 4. Access Your Cluster

#### Option A: SSH into the container
```bash
ssh -p 2222 k0s@localhost
# Password: k0s

# Inside the container, use kubectl
k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get nodes
```

#### Option B: Copy kubeconfig to your Mac
```bash
# Copy kubeconfig from container
scp -P 2222 k0s@localhost:/home/<USER>/kubeconfig ~/.kube/k0s-local-config

# Use kubectl from your Mac (if kubectl is installed)
kubectl --kubeconfig ~/.kube/k0s-local-config get nodes
```

## Configuration

### Container Ports

- `2222`: SSH access to the container
- `6443`: Kubernetes API server
- `8080`: K0s API (health checks)
- `10250`: Kubelet API

### Default Credentials

- SSH User: `k0s`
- SSH Password: `k0s`
- Container has sudo access without password

### Network Configuration

- Pod CIDR: `**********/16`
- Service CIDR: `*********/12`
- Container Network: `**********/16`

## Customization

### Modify K0s Configuration

Edit `ansible/templates/k0s-config.yaml.j2` to customize:
- Network settings
- API server configuration
- Storage backend
- Security policies

### Change Container Settings

Edit `docker-compose.yml` to modify:
- Port mappings
- Volume mounts
- Environment variables
- Resource limits

### Ansible Variables

Modify `ansible/inventory.ini` to change:
- K0s version
- Cluster name
- Network CIDRs
- Directory paths

## Troubleshooting

### Container Won't Start
```bash
# Check Docker logs
docker-compose logs k0s-local

# Restart the container
docker-compose restart k0s-local
```

### SSH Connection Issues
```bash
# Test SSH connectivity
ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost

# Check if SSH service is running in container
docker-compose exec k0s-local systemctl status ssh
```

### K0s Service Issues
```bash
# SSH into container and check K0s status
ssh -p 2222 k0s@localhost
sudo systemctl status k0scontroller
sudo journalctl -u k0scontroller -f
```

### Ansible Playbook Fails
```bash
# Run with verbose output
ansible-playbook -i ansible/inventory.ini ansible/playbook.yml -vvv

# Test connectivity
ansible -i ansible/inventory.ini k0s_cluster -m ping
```

## Useful Commands

### Container Management
```bash
# Start the cluster
docker-compose up -d

# Stop the cluster
docker-compose down

# Rebuild and restart
docker-compose up -d --build --force-recreate

# View logs
docker-compose logs -f k0s-local
```

### Cluster Operations
```bash
# SSH into container
ssh -p 2222 k0s@localhost

# Check cluster status
k0s status

# Get cluster info
k0s kubectl --kubeconfig=/home/<USER>/kubeconfig cluster-info

# List all pods
k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get pods --all-namespaces
```

### Cleanup
```bash
# Stop and remove containers, networks, volumes
docker-compose down -v

# Remove the built image
docker rmi k0s-local_k0s-local
```

## Next Steps

Once your cluster is running, you can:

1. Deploy sample applications
2. Install Helm charts
3. Set up ingress controllers
4. Configure persistent storage
5. Add monitoring and logging

## Security Notes

⚠️ **This setup is for development/testing only!**

- Uses default passwords
- Runs containers in privileged mode
- Disables SSH host key checking
- Not suitable for production use

For production deployments, consider:
- Using proper authentication
- Implementing network policies
- Enabling RBAC
- Using secrets management
- Regular security updates
