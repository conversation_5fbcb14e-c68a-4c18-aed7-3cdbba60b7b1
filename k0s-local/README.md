# K0s Local Kubernetes Cluster ✅ FULLY OPERATIONAL

A complete, automated K0s single-node Kubernetes cluster running inside a Docker container on your Mac. **All issues resolved and fully tested!**

## 🎉 Status: FULLY WORKING

✅ **All tests passing!** This setup has been thoroughly tested and debugged.

## 🏗️ Architecture

```
k0s-local/
├── Dockerfile              # Container definition: Ubuntu 22.04 + K0s + dependencies
├── docker-compose.yml      # Orchestration: ports, volumes, networking
├── test-cluster.sh         # Automated testing: validates entire setup
├── Makefile               # Convenience commands: up, down, logs, ssh, etc.
├── README.md              # Documentation: setup guide and usage
├── TROUBLESHOOTING.md     # Complete troubleshooting guide with all fixes
├── config/
│   └── supervisord.conf   # Service management: SSH daemon configuration
└── ansible/
    ├── inventory.ini      # Target hosts: container connection details
    ├── playbook.yml       # Deployment automation: K0s installation & config
    └── templates/
        └── k0s.yaml.j2    # K0s configuration template: cluster settings
```

### 📁 File Descriptions

#### **Core Infrastructure Files**

**`Dockerfile`**
- **Purpose**: Defines the container image with Ubuntu 22.04 base
- **Key Components**:
  - Installs K0s binary, SSH server, supervisor, Ansible dependencies
  - Creates k0s user with sudo privileges
  - Sets up supervisor for service management (replaces systemd)
  - Configures SSH with password authentication

**`docker-compose.yml`**
- **Purpose**: Container orchestration and networking
- **Configuration**:
  - Port mappings: 2222→22 (SSH), 6443→6443 (K8s API), 8080→8080, 10250→10250
  - Volume mounts for persistent data
  - Network configuration with custom bridge
  - Health checks and restart policies

#### **Automation & Testing**

**`test-cluster.sh`**
- **Purpose**: Comprehensive automated testing suite
- **Tests Performed**:
  - Docker connectivity and container health
  - SSH access with password authentication
  - K0s installation and service status
  - Kubernetes API responsiveness
  - Pod deployment and management
- **Features**: Uses sshpass for automated password authentication

**`Makefile`**
- **Purpose**: Convenience commands for common operations
- **Commands**: up, down, logs, ssh, status, test, clean
- **Benefits**: Simplifies complex docker-compose and SSH commands

#### **Configuration Management**

**`config/supervisord.conf`**
- **Purpose**: Service management configuration (replaces systemd)
- **Services Managed**:
  - SSH daemon with proper directory creation
  - Process supervision and automatic restart
  - Logging configuration
- **Why Supervisor**: Container-friendly alternative to systemd

#### **Ansible Automation**

**`ansible/inventory.ini`**
- **Purpose**: Defines target hosts and connection parameters
- **Configuration**:
  - Container connection details (localhost:2222)
  - SSH credentials and options
  - Ansible variables for K0s setup

**`ansible/playbook.yml`**
- **Purpose**: Complete K0s deployment automation
- **Tasks Performed**:
  - Downloads and installs K0s binary
  - Creates configuration directories
  - Generates K0s cluster configuration
  - Starts K0s controller service
  - Validates cluster health

**`ansible/templates/k0s.yaml.j2`**
- **Purpose**: K0s cluster configuration template
- **Settings**:
  - API server configuration (auto-detects container IP)
  - Network CIDRs for pods and services
  - Storage backend (etcd)
  - Security policies and worker profiles

#### **Documentation Files**

**`README.md`**
- **Purpose**: Main documentation and setup guide
- **Contents**:
  - Quick start instructions
  - Architecture overview with file descriptions
  - Configuration details and customization options
  - Troubleshooting and usage examples

**`TROUBLESHOOTING.md`**
- **Purpose**: Comprehensive troubleshooting and project history
- **Contents**:
  - Complete error log with all issues encountered and fixes
  - Original project goals vs. current implementation
  - Detailed test results and validation
  - Next steps and future enhancements roadmap
  - Architecture decisions and lessons learned

### 🔄 Data Flow

1. **Build Phase**: Dockerfile creates container with all dependencies
2. **Startup Phase**: docker-compose starts container with supervisor managing SSH
3. **Deployment Phase**: Ansible playbook configures and starts K0s cluster
4. **Validation Phase**: test-cluster.sh verifies all components are working
5. **Usage Phase**: SSH access for kubectl commands and cluster management

## 📋 Prerequisites

- Docker Desktop installed and running
- Docker Compose
- Ansible installed on your Mac
- sshpass (for automated testing)

### Install Prerequisites (macOS)

```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required tools
brew install ansible sshpass
```

## 🚀 Quick Start

### **Option 1: One Command Setup (Recommended)**
```bash
# Complete setup: build + start + deploy + test
make setup
```

### **Option 2: Manual Steps (4 Commands)**
```bash
# 1. Start the container infrastructure
docker-compose up -d --build

# 2. Deploy K0s cluster with Ansible
ansible-playbook -i ansible/inventory.ini ansible/playbook.yml

# 3. Run automated tests (verifies everything works)
./test-cluster.sh

# 4. SSH into your cluster
ssh -p 2222 k0s@localhost
# Password: k0s
```

⚠️ **Important**: The container only provides the infrastructure. You must run the Ansible playbook to actually deploy and start the K0s cluster!

### Expected Test Output ✅

```bash
🚀 Testing K0s Local Cluster Setup
==================================
1. Checking Docker...                    ✓ Docker is running
2. Checking container status...          ✓ K0s container is running
3. Testing SSH connectivity...           ✓ SSH connection successful
4. Checking K0s installation...          ✅ K0s binary is installed
5. Checking K0s service...               ✅ K0s controller is running
6. Testing Kubernetes API...             ✅ Kubernetes API is responding
7. Testing sample deployment...          ✅ Sample pod deployment successful

🎉 K0s Local Cluster Test Complete!
```

### Using Your Cluster

#### **With Makefile (Recommended)**
```bash
# SSH into cluster
make ssh

# Check cluster status
make status

# Copy kubeconfig to local machine
make kubeconfig

# Run tests
make test

# View logs
make logs

# Clean up everything
make clean
```

#### **Manual Commands**
```bash
# SSH into the cluster
ssh -p 2222 k0s@localhost
# Password: k0s

# Inside the cluster, use kubectl
sudo k0s kubectl get nodes
sudo k0s kubectl get pods --all-namespaces
sudo k0s kubectl cluster-info

# Generate kubeconfig for external use
sudo k0s kubeconfig admin > kubeconfig

# Deploy a test application
sudo k0s kubectl run nginx --image=nginx
sudo k0s kubectl get pods
```

## ⚙️ Configuration

### Container Ports

- `2222`: SSH access to the container
- `6443`: Kubernetes API server
- `8080`: Additional services
- `10250`: Kubelet API

### Default Credentials

- SSH User: `k0s`
- SSH Password: `k0s`
- Container has sudo access without password

### Network Configuration

- Pod CIDR: `**********/16`
- Service CIDR: `*********/12`
- Container Network: `**********/16`

### Key Features ✅

- **Supervisor**: Uses supervisor instead of systemd (container-friendly)
- **Automated Setup**: Ansible playbook handles all configuration
- **Password SSH**: Simple password authentication for development
- **Automated Testing**: Comprehensive test script validates everything
- **Persistent Storage**: Container data persists between restarts

## Customization

### Modify K0s Configuration

Edit `ansible/templates/k0s-config.yaml.j2` to customize:
- Network settings
- API server configuration
- Storage backend
- Security policies

### Change Container Settings

Edit `docker-compose.yml` to modify:
- Port mappings
- Volume mounts
- Environment variables
- Resource limits

### Ansible Variables

Modify `ansible/inventory.ini` to change:
- K0s version
- Cluster name
- Network CIDRs
- Directory paths

## Troubleshooting

### Container Won't Start
```bash
# Check Docker logs
docker-compose logs k0s-local

# Restart the container
docker-compose restart k0s-local
```

### SSH Connection Issues
```bash
# Test SSH connectivity
ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost

# Check if SSH service is running in container
docker-compose exec k0s-local systemctl status ssh
```

### K0s Service Issues
```bash
# SSH into container and check K0s status
ssh -p 2222 k0s@localhost
sudo systemctl status k0scontroller
sudo journalctl -u k0scontroller -f
```

### Ansible Playbook Fails
```bash
# Run with verbose output
ansible-playbook -i ansible/inventory.ini ansible/playbook.yml -vvv

# Test connectivity
ansible -i ansible/inventory.ini k0s_cluster -m ping
```

## Useful Commands

### Container Management
```bash
# Start the cluster
docker-compose up -d

# Stop the cluster
docker-compose down

# Rebuild and restart
docker-compose up -d --build --force-recreate

# View logs
docker-compose logs -f k0s-local
```

### Cluster Operations
```bash
# SSH into container
ssh -p 2222 k0s@localhost

# Check cluster status
k0s status

# Get cluster info
k0s kubectl --kubeconfig=/home/<USER>/kubeconfig cluster-info

# List all pods
k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get pods --all-namespaces
```

### Cleanup
```bash
# Stop and remove containers, networks, volumes
docker-compose down -v

# Remove the built image
docker rmi k0s-local_k0s-local
```

## Next Steps

Once your cluster is running, you can:

1. Deploy sample applications
2. Install Helm charts
3. Set up ingress controllers
4. Configure persistent storage
5. Add monitoring and logging

## Security Notes

⚠️ **This setup is for development/testing only!**

- Uses default passwords
- Runs containers in privileged mode
- Disables SSH host key checking
- Not suitable for production use

For production deployments, consider:
- Using proper authentication
- Implementing network policies
- Enabling RBAC
- Using secrets management
- Regular security updates
