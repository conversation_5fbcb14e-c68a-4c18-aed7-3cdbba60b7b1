#!/bin/bash

# Direct K0s startup script - bypasses Ansible issues
set -e

echo "🚀 Starting K0s cluster directly..."

# Kill any existing K0s processes
echo "Cleaning up any existing K0s processes..."
pkill -f "k0s controller" || true
sleep 2

# Ensure directories exist
echo "Creating necessary directories..."
mkdir -p /etc/k0s
mkdir -p /var/lib/k0s
mkdir -p /var/log

# Create K0s configuration
echo "Creating K0s configuration..."
cat > /etc/k0s/k0s.yaml << 'EOF'
apiVersion: k0s.k0sproject.io/v1beta1
kind: ClusterConfig
metadata:
  name: k0s-local
spec:
  api:
    port: 6443
    sans:
      - 127.0.0.1
      - localhost
      - k0s-local
  controllerManager: {}
  scheduler: {}
  storage:
    type: etcd
  network:
    kubeProxy:
      disabled: false
    kuberouter:
      autoMTU: true
    podCIDR: **********/16
    serviceCIDR: *********/12
  podSecurityPolicy:
    defaultPolicy: 00-k0s-privileged
EOF

# Start K0s controller
echo "Starting K0s controller..."
nohup k0s controller --config /etc/k0s/k0s.yaml > /var/log/k0s-controller.log 2>&1 &

# Wait for startup
echo "Waiting for K0s to start..."
sleep 15

# Check if K0s is running
echo "Checking K0s status..."
for i in {1..30}; do
    if k0s status >/dev/null 2>&1; then
        echo "✅ K0s is running!"
        k0s status
        break
    fi
    echo "Waiting for K0s... (attempt $i/30)"
    sleep 2
done

# Test API server
echo "Testing API server..."
for i in {1..10}; do
    if k0s kubectl get nodes >/dev/null 2>&1; then
        echo "✅ API server is responding!"
        echo "📊 Cluster info:"
        k0s kubectl cluster-info
        echo ""
        echo "📋 System pods:"
        k0s kubectl get pods --all-namespaces
        break
    fi
    echo "Waiting for API server... (attempt $i/10)"
    sleep 3
done

echo "🎉 K0s cluster startup complete!"
